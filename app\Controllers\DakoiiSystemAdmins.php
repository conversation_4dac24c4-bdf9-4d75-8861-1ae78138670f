<?php

namespace App\Controllers;

use App\Models\UsersModel;
use App\Models\DakoiiOrgModel;

/**
 * Dakoii System Administrators Controller
 *
 * Manages system administrators (users with role 'admin') from the main users table
 * These are organization administrators, not Dakoii portal users
 */
class DakoiiSystemAdmins extends BaseController
{
    protected $usersModel;
    protected $orgModel;
    protected $session;

    public function __construct()
    {
        $this->usersModel = new UsersModel();
        $this->orgModel = new DakoiiOrgModel();
        $this->session = \Config\Services::session();

        // Load helpers
        helper(['form', 'url', 'info']);
    }

    /**
     * Display system administrators list
     */
    public function index()
    {
        // Check authentication
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        $data['title'] = "System Administrators";
        $data['menu'] = "system_admins";

        // Get all admin users with organization details
        // Note: Join using org_id relationship between dakoii_org.id and users.org_id
        $data['admins'] = $this->usersModel->select('users.*, dakoii_org.name as org_name, dakoii_org.id as org_table_id, dakoii_org.orgcode')
                                          ->join('dakoii_org', 'dakoii_org.id = users.org_id', 'left')
                                          ->where('users.role', 'admin')
                                          ->orderBy('users.created_at', 'DESC')
                                          ->findAll();

        return view('dakoii/dakoii_system_admins_index', $data);
    }

    /**
     * Show create system administrator form
     */
    public function create()
    {
        // Check authentication
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        $data['title'] = "Create System Administrator";
        $data['menu'] = "system_admins";
        $data['organizations'] = $this->orgModel->where('is_active', 1)->findAll();

        return view('dakoii/dakoii_system_admins_create', $data);
    }

    /**
     * Store new system administrator
     */
    public function store()
    {
        // Check authentication
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        // Simple validation
        if (empty($this->request->getPost('name'))) {
            session()->setFlashdata('error', 'Administrator name is required');
            return redirect()->to('dakoii/system-admins/create');
        }

        if (empty($this->request->getPost('email'))) {
            session()->setFlashdata('error', 'Email is required');
            return redirect()->to('dakoii/system-admins/create');
        }

        if (empty($this->request->getPost('password'))) {
            session()->setFlashdata('error', 'Password is required');
            return redirect()->to('dakoii/system-admins/create');
        }

        $orgcode = $this->request->getPost('orgcode');
        if (empty($orgcode)) {
            session()->setFlashdata('error', 'Please select an organization');
            return redirect()->to('dakoii/system-admins/create');
        }

        // Check if email already exists
        $existingUser = $this->usersModel->where('email', $this->request->getPost('email'))->first();
        if ($existingUser) {
            session()->setFlashdata('error', 'Email already exists');
            return redirect()->to('dakoii/system-admins/create');
        }

        // Get organization details
        $org = $this->orgModel->where('orgcode', $orgcode)->first();
        if (!$org) {
            session()->setFlashdata('error', 'Invalid organization selected');
            return redirect()->to('dakoii/system-admins/create');
        }

        $data = [
            'org_id' => $org['id'],
            'name' => $this->request->getPost('name'),
            'email' => $this->request->getPost('email'),
            'password' => password_hash($this->request->getPost('password'), PASSWORD_DEFAULT),
            'role' => 'admin', // Fixed as admin
            'position' => $this->request->getPost('position') ?: '',
            'phone' => $this->request->getPost('phone') ?: '',
            'status' => $this->request->getPost('status') ? 1 : 0,
            'created_by' => $this->session->get('dakoii_name')
        ];

        if ($this->usersModel->insert($data)) {
            session()->setFlashdata('success', 'System administrator created successfully!');
            return redirect()->to('dakoii/system-admins');
        } else {
            session()->setFlashdata('error', 'Failed to create system administrator');
            return redirect()->to('dakoii/system-admins/create');
        }
    }

    /**
     * Show system administrator details
     */
    public function show($id)
    {
        // Check authentication
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        $admin = $this->usersModel->select('users.*, dakoii_org.name as org_name, dakoii_org.id as org_table_id, dakoii_org.orgcode')
                                 ->join('dakoii_org', 'dakoii_org.id = users.org_id', 'left')
                                 ->where('users.id', $id)
                                 ->where('users.role', 'admin')
                                 ->first();

        if (!$admin) {
            session()->setFlashdata('error', 'System administrator not found');
            return redirect()->to('dakoii/system-admins');
        }

        $data['title'] = "System Administrator - " . $admin['name'];
        $data['menu'] = "system_admins";
        $data['admin'] = $admin;

        return view('dakoii/dakoii_system_admins_show', $data);
    }

    /**
     * Show edit system administrator form
     */
    public function edit($id)
    {
        // Check authentication
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        $admin = $this->usersModel->select('users.*, dakoii_org.name as org_name, dakoii_org.id as org_table_id, dakoii_org.orgcode')
                                 ->join('dakoii_org', 'dakoii_org.id = users.org_id', 'left')
                                 ->where('users.id', $id)
                                 ->where('users.role', 'admin')
                                 ->first();

        if (!$admin) {
            session()->setFlashdata('error', 'System administrator not found');
            return redirect()->to('dakoii/system-admins');
        }

        $data['title'] = "Edit System Administrator - " . $admin['name'];
        $data['menu'] = "system_admins";
        $data['admin'] = $admin;
        $data['organizations'] = $this->orgModel->where('is_active', 1)->findAll();

        return view('dakoii/dakoii_system_admins_edit', $data);
    }

    /**
     * Update system administrator
     */
    public function update($id)
    {
        // Check authentication
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        $admin = $this->usersModel->where('id', $id)
                                 ->where('role', 'admin')
                                 ->first();

        if (!$admin) {
            session()->setFlashdata('error', 'System administrator not found');
            return redirect()->to('dakoii/system-admins');
        }

        // Validate input
        $rules = [
            'orgcode' => 'required',
            'name' => 'required|min_length[3]|max_length[255]',
            'email' => "required|valid_email|max_length[500]|is_unique[users.email,id,{$id}]",
            'phone' => 'permit_empty|max_length[200]',
            'position' => 'permit_empty|max_length[255]'
        ];

        // Only validate password if provided
        if (!empty($this->request->getPost('password'))) {
            $rules['password'] = 'min_length[6]';
        }

        if (!$this->validate($rules)) {
            session()->setFlashdata('error', 'Please check your input: ' . implode(', ', $this->validator->getErrors()));
            return redirect()->to('dakoii/system-admins/edit/' . $id)->withInput();
        }

        // Get organization details
        $org = $this->orgModel->where('orgcode', $this->request->getPost('orgcode'))->first();
        if (!$org) {
            session()->setFlashdata('error', 'Invalid organization selected');
            return redirect()->to('dakoii/system-admins/edit/' . $id)->withInput();
        }

        $data = [
            'org_id' => $org['id'],
            'name' => trim($this->request->getPost('name')),
            'email' => trim($this->request->getPost('email')),
            'role' => $this->request->getPost('role'), // Add role update
            'position' => trim($this->request->getPost('position')),
            'phone' => trim($this->request->getPost('phone')),
            'status' => $this->request->getPost('status') ? 1 : 0,
            'updated_by' => $this->session->get('dakoii_name')
        ];

        // Update password only if provided
        if (!empty($this->request->getPost('password'))) {
            $data['password'] = password_hash($this->request->getPost('password'), PASSWORD_DEFAULT);
        }

        if ($this->usersModel->update($id, $data)) {
            session()->setFlashdata('success', 'System administrator "' . $data['name'] . '" updated successfully!');
            return redirect()->to('dakoii/system-admins/show/' . $id);
        } else {
            session()->setFlashdata('error', 'Failed to update system administrator. Please try again.');
            return redirect()->to('dakoii/system-admins/edit/' . $id)->withInput();
        }
    }

    /**
     * Delete system administrator
     */
    public function delete($id)
    {
        // Check authentication
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        $admin = $this->usersModel->where('id', $id)
                                 ->where('role', 'admin')
                                 ->first();

        if (!$admin) {
            session()->setFlashdata('error', 'System administrator not found');
            return redirect()->to('dakoii/system-admins');
        }

        if ($this->usersModel->delete($id)) {
            session()->setFlashdata('success', 'System administrator deleted successfully!');
        } else {
            session()->setFlashdata('error', 'Failed to delete system administrator');
        }

        return redirect()->to('dakoii/system-admins');
    }

    /**
     * Show administrators for a specific organization
     */
    public function orgAdmins($orgId)
    {
        // Check authentication
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        $org = $this->orgModel->find($orgId);
        if (!$org) {
            session()->setFlashdata('error', 'Organization not found');
            return redirect()->to('dakoii/organizations');
        }

        $data['title'] = "Administrators - " . $org['name'];
        $data['menu'] = "organizations";
        $data['org'] = $org;

        // Get administrators for this organization
        $data['admins'] = $this->usersModel->where('org_id', $orgId)
                                          ->where('role', 'admin')
                                          ->orderBy('created_at', 'DESC')
                                          ->findAll();

        return view('dakoii/dakoii_org_admins_index', $data);
    }

    /**
     * Show create administrator form for specific organization
     */
    public function createForOrg($orgId)
    {
        // Check authentication
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        $org = $this->orgModel->find($orgId);
        if (!$org) {
            session()->setFlashdata('error', 'Organization not found');
            return redirect()->to('dakoii/organizations');
        }

        $data['title'] = "Add Administrator - " . $org['name'];
        $data['menu'] = "organizations";
        $data['org'] = $org;

        return view('dakoii/dakoii_org_admins_create', $data);
    }

    /**
     * Store new administrator for specific organization
     */
    public function storeForOrg($orgId)
    {
        // Check authentication
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        $org = $this->orgModel->find($orgId);
        if (!$org) {
            session()->setFlashdata('error', 'Organization not found');
            return redirect()->to('dakoii/organizations');
        }

        // Simple validation
        if (empty($this->request->getPost('name'))) {
            session()->setFlashdata('error', 'Administrator name is required');
            return redirect()->to('dakoii/organizations/' . $orgId . '/admins/create');
        }

        if (empty($this->request->getPost('email'))) {
            session()->setFlashdata('error', 'Email is required');
            return redirect()->to('dakoii/organizations/' . $orgId . '/admins/create');
        }

        if (empty($this->request->getPost('password'))) {
            session()->setFlashdata('error', 'Password is required');
            return redirect()->to('dakoii/organizations/' . $orgId . '/admins/create');
        }

        // Check if email already exists
        $existingUser = $this->usersModel->where('email', $this->request->getPost('email'))->first();
        if ($existingUser) {
            session()->setFlashdata('error', 'Email already exists');
            return redirect()->to('dakoii/organizations/' . $orgId . '/admins/create');
        }

        $data = [
            'org_id' => $orgId,
            'name' => $this->request->getPost('name'),
            'email' => $this->request->getPost('email'),
            'password' => password_hash($this->request->getPost('password'), PASSWORD_DEFAULT),
            'role' => 'admin', // Fixed as admin
            'position' => $this->request->getPost('position') ?: '',
            'phone' => $this->request->getPost('phone') ?: '',
            'status' => $this->request->getPost('status') ? 1 : 0,
            'created_by' => $this->session->get('dakoii_name')
        ];

        if ($insertId = $this->usersModel->insert($data)) {
            session()->setFlashdata('success', 'Administrator created successfully!');
            return redirect()->to('dakoii/organizations/show/' . $orgId);
        } else {
            session()->setFlashdata('error', 'Failed to create administrator');
            return redirect()->to('dakoii/organizations/' . $orgId . '/admins/create');
        }
    }

    /**
     * Check if user is authenticated for Dakoii portal
     */
    private function isAuthenticated(): bool
    {
        return $this->session->has('dakoii_logged_in') && $this->session->get('dakoii_logged_in') === true;
    }
}

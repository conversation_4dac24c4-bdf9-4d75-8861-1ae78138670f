<?= $this->extend("templates/dakoii_template"); ?>
<?= $this->section('content'); ?>

<!-- Breadcrumb -->
<nav aria-label="breadcrumb" class="mb-4">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="<?= base_url('dakoii/dashboard') ?>">Dashboard</a></li>
        <li class="breadcrumb-item"><a href="<?= base_url('dakoii/locations') ?>">Locations</a></li>
        <li class="breadcrumb-item"><a href="<?= base_url('dakoii/locations/provinces') ?>">Provinces</a></li>
        <li class="breadcrumb-item active">Import Districts CSV</li>
    </ol>
</nav>

<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h2 class="mb-0">Import Districts for <?= esc($province['name']) ?></h2>
        <p class="text-muted mb-0">Upload a CSV file to import multiple districts for this province</p>
    </div>
    <div class="btn-group">
        <a href="<?= base_url('dakoii/locations/districts/sample-csv/' . $province['id']) ?>" class="btn btn-outline-info">
            <i class="fas fa-download"></i> Download Sample CSV
        </a>
        <a href="<?= base_url('dakoii/locations/districts/' . $province['id']) ?>" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left"></i> Back to Districts
        </a>
    </div>
</div>

<!-- Flash Messages -->
<?php if (session()->getFlashdata('error')): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="fas fa-exclamation-circle"></i> 
        <pre style="white-space: pre-wrap; margin: 0;"><?= session()->getFlashdata('error') ?></pre>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<?php if (session()->getFlashdata('success')): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="fas fa-check-circle"></i> 
        <pre style="white-space: pre-wrap; margin: 0;"><?= session()->getFlashdata('success') ?></pre>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<div class="row">
    <!-- Import Form -->
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-upload"></i> Upload CSV File
                </h5>
            </div>
            <div class="card-body">
                <?= form_open_multipart('dakoii/locations/districts/import/' . $province['id']) ?>
                <?= csrf_field() ?>

                <div class="mb-4">
                    <label class="form-label">CSV File <span class="text-danger">*</span></label>
                    <input type="file" class="form-control" name="csv_file" accept=".csv" required>
                    <div class="form-text">
                        Select a CSV file containing district data. Maximum file size: 2MB
                    </div>
                </div>

                <div class="d-flex justify-content-between">
                    <a href="<?= base_url('dakoii/locations/districts/' . $province['id']) ?>" class="btn btn-secondary">
                        <i class="fas fa-times"></i> Cancel
                    </a>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-upload"></i> Import Districts
                    </button>
                </div>

                <?= form_close() ?>
            </div>
        </div>

        <!-- Province Information -->
        <div class="card mt-4">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-info-circle"></i> Province Information
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <strong>Province Name:</strong><br>
                        <span class="badge bg-primary"><?= esc($province['name']) ?></span>
                    </div>
                    <div class="col-md-6">
                        <strong>Province Code:</strong><br>
                        <span class="badge bg-secondary"><?= esc($province['provincecode']) ?></span>
                    </div>
                </div>
                <div class="alert alert-info mt-3">
                    <i class="fas fa-info-circle"></i>
                    All districts will be imported under this province.
                </div>
            </div>
        </div>
    </div>

    <!-- Instructions Panel -->
    <div class="col-lg-4">
        <!-- CSV Format Instructions -->
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-info-circle"></i> CSV Format Requirements
                </h6>
            </div>
            <div class="card-body">
                <h6>Required Columns:</h6>
                <ul class="list-unstyled">
                    <li><strong>districtcode</strong> - District code (unique)</li>
                    <li><strong>name</strong> - District name</li>
                </ul>

                <h6 class="mt-3">Example:</h6>
                <div class="bg-light p-2 rounded">
                    <code>
                        districtcode,name<br>
                        1401,"Ambunti/Drekikier"<br>
                        1402,"Angoram"<br>
                        1403,"Maprik"
                    </code>
                </div>

                <div class="mt-3">
                    <a href="<?= base_url('dakoii/locations/districts/sample-csv/' . $province['id']) ?>" class="btn btn-sm btn-outline-info">
                        <i class="fas fa-download"></i> Download Sample
                    </a>
                </div>
            </div>
        </div>

        <!-- Import Notes -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-exclamation-triangle"></i> Important Notes
                </h6>
            </div>
            <div class="card-body">
                <ul class="small">
                    <li>District codes must be unique</li>
                    <li>All districts will be assigned to <?= esc($province['name']) ?></li>
                    <li>Duplicate codes will be skipped</li>
                    <li>Empty rows will be ignored</li>
                    <li>Maximum file size is 2MB</li>
                    <li>Only CSV files are accepted</li>
                </ul>

                <div class="alert alert-warning mt-3">
                    <i class="fas fa-info-circle"></i>
                    <strong>Note:</strong> All imported districts will belong to the selected province.
                </div>
            </div>
        </div>
    </div>
</div>

<?= $this->endSection() ?>

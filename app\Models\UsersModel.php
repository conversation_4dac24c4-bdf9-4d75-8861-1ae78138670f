<?php namespace App\Models;

use CodeIgniter\Model;

class UsersModel extends Model
{
    protected $table      = 'users';
    protected $primaryKey = 'id';

    protected $useAutoIncrement = true;

    protected $returnType     = 'array';
    protected $useSoftDeletes = false;

    protected $allowedFields = [
        'org_id',
        'sys_no',
        'name',
        'password',
        'role',
        'position',
        'id_photo',
        'phone',
        'email',
        'status',
        'status_at',
        'status_by',
        'status_remarks',
        'created_by',
        'updated_by'
    ];

    protected $useTimestamps = true;
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $deletedField  = '';

    protected $validationRules    = [];
    protected $validationMessages = [];
    protected $skipValidation     = false;

    /**
     * Generate unique system number based on year + increment
     * Format: YYYY + increment (e.g., 202501, 202502, etc.)
     */
    public function generateSysNo()
    {
        $currentYear = date('Y');

        // Get the highest sys_no for the current year
        $query = $this->db->query("SELECT MAX(sys_no) as max_sys_no FROM users WHERE sys_no LIKE '{$currentYear}%'");
        $result = $query->getRow();

        if ($result && $result->max_sys_no) {
            // Extract the increment part and add 1
            $lastIncrement = (int)substr($result->max_sys_no, 4);
            $newIncrement = $lastIncrement + 1;
        } else {
            // First user for this year
            $newIncrement = 1;
        }

        // Format: YYYY + increment (pad increment to ensure proper ordering)
        return $currentYear . str_pad($newIncrement, 2, '0', STR_PAD_LEFT);
    }

    /**
     * Override insert method to automatically generate sys_no
     */
    public function insert($data = null, bool $returnID = true)
    {
        if (is_array($data)) {
            // Generate sys_no if not provided
            if (!isset($data['sys_no']) || empty($data['sys_no'])) {
                $data['sys_no'] = $this->generateSysNo();
            }
        }

        return parent::insert($data, $returnID);
    }

    /**
     * Get user by system number
     */
    public function getUserBySysNo($sysNo)
    {
        return $this->where('sys_no', $sysNo)->first();
    }

    /**
     * Get all admin users for a specific organization
     */
    public function getAdminsByOrgId($orgId)
    {
        return $this->where('org_id', $orgId)
                   ->where('role', 'admin')
                   ->findAll();
    }

    /**
     * Get all users for a specific organization
     */
    public function getUsersByOrgId($orgId)
    {
        return $this->where('org_id', $orgId)->findAll();
    }
}
?>
<?php

namespace App\Controllers;

use App\Models\AdxCountryModel;
use App\Models\DakoiiUsersModel;
use App\Models\DakoiiOrgModel;
use App\Models\AdxProvinceModel;
use App\Models\UsersModel;

class DakoiiOrganizations extends BaseController
{
    public $session;
    public $dusersModel;
    public $usersModel;
    public $orgModel;
    public $countryModel;
    public $provinceModel;

    public function __construct()
    {
        helper(['form', 'url', 'info']);
        $this->session = session();

        // Initialize models
        $this->dusersModel = new DakoiiUsersModel();
        $this->usersModel = new UsersModel();
        $this->orgModel = new DakoiiOrgModel();
        $this->countryModel = new AdxCountryModel();
        $this->provinceModel = new AdxProvinceModel();
    }

    /**
     * Display organizations list
     */
    public function index()
    {
        // Check authentication
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        $data['title'] = "Organizations Management";
        $data['menu'] = "organizations";
        $data['organizations'] = $this->orgModel->orderBy('id', 'DESC')->findAll();

        return view('dakoii/dakoii_organizations_index', $data);
    }

    /**
     * Show create organization form
     */
    public function create()
    {
        // Check authentication
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        $data['title'] = "Create Organization";
        $data['menu'] = "organizations";
        $data['countries'] = $this->countryModel->findAll();
        $data['provinces'] = $this->provinceModel->findAll();

        return view('dakoii/dakoii_organizations_create', $data);
    }

    /**
     * Store new organization
     */
    public function store()
    {
        // Check authentication
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        // Simple validation - just check if name is provided
        if (empty($this->request->getPost('name'))) {
            session()->setFlashdata('error', 'Organization name is required');
            return redirect()->to('dakoii/organizations/create');
        }

        // Generate unique organization code
        $orgcode = rand(11111, 99999);
        while (!empty($this->orgModel->where('orgcode', $orgcode)->first())) {
            $orgcode = rand(11111, 99999);
        }

        $data = [
            'orgcode' => $orgcode,
            'name' => $this->request->getPost('name'),
            'description' => $this->request->getPost('description') ?: '',
            'addlockcountry' => $this->request->getPost('country') ?: '',
            'addlockprov' => $this->request->getPost('province') ?: '',
            'is_locationlocked' => $this->request->getPost('country') ? 1 : 0,
            'is_active' => 1,
            'license_status' => 'trial'
        ];

        // Handle logo upload
        $logoFile = $this->request->getFile('org_logo');
        if ($logoFile && $logoFile->isValid() && $logoFile->getSize() > 0) {
            $newName = $orgcode . "_" . time() . '.' . $logoFile->getExtension();

            // Create upload directory if it doesn't exist
            $uploadPath = ROOTPATH . 'public/uploads/org_logo/';
            if (!is_dir($uploadPath)) {
                mkdir($uploadPath, 0755, true);
            }

            $logoFile->move($uploadPath, $newName);
            $data['orglogo'] = base_url() . 'public/uploads/org_logo/' . $newName;
        }

        if ($insertId = $this->orgModel->insert($data)) {
            session()->setFlashdata('success', 'Organization created successfully!');
            return redirect()->to('dakoii/organizations');
        } else {
            session()->setFlashdata('error', 'Failed to create organization');
            return redirect()->to('dakoii/organizations/create');
        }
    }

    /**
     * Show organization details
     */
    public function show($id)
    {
        // Check authentication
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        $org = $this->orgModel->find($id);
        if (!$org) {
            session()->setFlashdata('error', 'Organization not found');
            return redirect()->to('dakoii/organizations');
        }

        $data['title'] = "Organization Details - " . $org['name'];
        $data['menu'] = "organizations";
        $data['org'] = $org;

        // Get organization system administrators from main users table
        // Note: Join using org_id relationship between dakoii_org.id and users.org_id
        $usersModel = new \App\Models\UsersModel();

        // Debug: Let's see what we're getting
        $allUsersForOrg = $usersModel->where('org_id', $org['id'])->findAll();
        $adminUsersForOrg = $usersModel->where('org_id', $org['id'])
                                      ->where('role', 'admin')
                                      ->findAll();

        // Temporary debug output
        log_message('debug', 'Organization ID: ' . $org['id']);
        log_message('debug', 'All users for org: ' . json_encode($allUsersForOrg));
        log_message('debug', 'Admin users for org: ' . json_encode($adminUsersForOrg));

        $data['admins'] = $adminUsersForOrg;

        // Get country and province names
        if (!empty($org['addlockcountry'])) {
            $country = $this->countryModel->find($org['addlockcountry']);
            $data['country_name'] = $country ? $country['name'] : 'Unknown';
        }

        if (!empty($org['addlockprov'])) {
            $province = $this->provinceModel->find($org['addlockprov']);
            $data['province_name'] = $province ? $province['name'] : 'Unknown';
        }

        // Get all countries and provinces for edit form
        $data['countries'] = $this->countryModel->findAll();
        $data['provinces'] = $this->provinceModel->findAll();

        return view('dakoii/dakoii_organizations_show', $data);
    }

    /**
     * Show edit organization form
     */
    public function edit($id)
    {
        // Check authentication
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        $org = $this->orgModel->find($id);
        if (!$org) {
            session()->setFlashdata('error', 'Organization not found');
            return redirect()->to('dakoii/organizations');
        }

        $data['title'] = "Edit Organization - " . $org['name'];
        $data['menu'] = "organizations";
        $data['org'] = $org;
        $data['countries'] = $this->countryModel->findAll();
        $data['provinces'] = $this->provinceModel->findAll();

        return view('dakoii/dakoii_organizations_edit', $data);
    }

    /**
     * Update organization
     */
    public function update($id)
    {
        // Check authentication
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        $org = $this->orgModel->find($id);
        if (!$org) {
            session()->setFlashdata('error', 'Organization not found');
            return redirect()->to('dakoii/organizations');
        }

        // Simple validation - just check if name is provided
        if (empty($this->request->getPost('name'))) {
            session()->setFlashdata('error', 'Organization name is required');
            return redirect()->to('dakoii/organizations/edit/' . $id);
        }

        $data = [
            'name' => $this->request->getPost('name'),
            'description' => $this->request->getPost('description') ?: '',
            'addlockcountry' => $this->request->getPost('country') ?: '',
            'addlockprov' => $this->request->getPost('province') ?: '',
            'is_locationlocked' => $this->request->getPost('country') ? 1 : 0,
            'is_active' => $this->request->getPost('status') ?: 1
        ];

        // Handle logo upload
        $logoFile = $this->request->getFile('org_logo');
        if ($logoFile && $logoFile->isValid() && $logoFile->getSize() > 0) {
            // Delete old logo if exists
            if (!empty($org['orglogo'])) {
                $oldLogoPath = str_replace(base_url(), ROOTPATH . 'public/', $org['orglogo']);
                if (file_exists($oldLogoPath)) {
                    unlink($oldLogoPath);
                }
            }

            $newName = $org['orgcode'] . "_" . time() . '.' . $logoFile->getExtension();

            // Create upload directory if it doesn't exist
            $uploadPath = ROOTPATH . 'public/uploads/org_logo/';
            if (!is_dir($uploadPath)) {
                mkdir($uploadPath, 0755, true);
            }

            $logoFile->move($uploadPath, $newName);
            $data['orglogo'] = base_url() . 'public/uploads/org_logo/' . $newName;
        }

        if ($this->orgModel->update($org['id'], $data)) {
            session()->setFlashdata('success', 'Organization updated successfully!');
            return redirect()->to('dakoii/organizations');
        } else {
            session()->setFlashdata('error', 'Failed to update organization');
            return redirect()->to('dakoii/organizations/edit/' . $id);
        }
    }

    /**
     * Update organization license status
     */
    public function updateLicense($id)
    {
        // Check authentication
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        $org = $this->orgModel->find($id);
        if (!$org) {
            session()->setFlashdata('error', 'Organization not found');
            return redirect()->to('dakoii/organizations');
        }

        $licenseStatus = $this->request->getPost('license_status');
        $data = ['license_status' => $licenseStatus];

        if ($this->orgModel->update($org['id'], $data)) {
            session()->setFlashdata('success', 'License status updated successfully!');
        } else {
            session()->setFlashdata('error', 'Failed to update license status');
        }

        return redirect()->to('dakoii/organizations/show/' . $id);
    }

    /**
     * Delete organization
     */
    public function delete($id)
    {
        // Check authentication
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        $org = $this->orgModel->find($id);
        if (!$org) {
            session()->setFlashdata('error', 'Organization not found');
            return redirect()->to('dakoii/organizations');
        }

        // Delete organization logo if exists
        if (!empty($org['orglogo'])) {
            $logoPath = str_replace(base_url(), ROOTPATH . 'public/', $org['orglogo']);
            if (file_exists($logoPath)) {
                unlink($logoPath);
            }
        }

        if ($this->orgModel->delete($id)) {
            session()->setFlashdata('success', 'Organization deleted successfully!');
        } else {
            session()->setFlashdata('error', 'Failed to delete organization');
        }

        return redirect()->to('dakoii/organizations');
    }

    /**
     * Check if user is authenticated for Dakoii portal
     */
    private function isAuthenticated(): bool
    {
        return $this->session->has('dakoii_logged_in') && $this->session->get('dakoii_logged_in') === true;
    }
}

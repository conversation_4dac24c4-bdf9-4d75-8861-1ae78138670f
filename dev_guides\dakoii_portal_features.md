# Dakoii Portal - Comprehensive Features Documentation

## Overview
The Dakoii Portal is a separate administrative interface within the AgriStats application, designed for system-level management of agricultural data, organizations, users, and locations. It operates independently from the main application with its own authentication system and user management.

## Core Architecture

### Authentication & Session Management
- **Independent Login System**: Separate authentication from main application
- **Session Isolation**: Prevents conflicts between main app and Dakoii portal sessions
- **Role-Based Access Control**: Multiple user roles (user, moderator, admin, super_admin)
- **Secure Authentication**: Password hashing and validation

### Database Tables
- `dakoii_users` - Portal system users
- `dakoii_org` - Organizations managed through portal
- Shared location tables (adx_country, adx_province, adx_district, adx_llg, adx_ward)
- Agricultural data tables (crops, fertilizers, pesticides, infections, livestock, education)

## Main Features

### 1. Dashboard & Analytics
- **System Overview**: Statistics for organizations, users, locations, and data
- **Quick Actions**: Fast access to common tasks
- **Province Statistics**: Geographic data distribution
- **Data Counts**: Real-time counts of all managed entities
- **License Status Tracking**: Monitor organization license payments

### 2. Organization Management
#### Features:
- **CRUD Operations**: Create, Read, Update, Delete organizations
- **Organization Profiles**: Complete organization information management
- **Location Locking**: Restrict organizations to specific geographic areas
- **License Management**: Track license status (paid/unpaid)
- **Logo Management**: Upload and manage organization logos
- **Status Control**: Activate/deactivate organizations

#### Organization Fields:
- Organization code (orgcode)
- Name and description
- Location restrictions (province, district, LLG)
- Logo upload capability
- License status tracking
- Activity status

### 3. User Management (Dakoii Portal Users)
#### Features:
- **User CRUD**: Complete user lifecycle management
- **Role Assignment**: user, moderator, admin, super_admin
- **Account Status**: Active/inactive user control
- **Password Management**: Secure password handling
- **Username Validation**: Unique username enforcement

#### User Roles:
- **User**: Basic access level
- **Moderator**: Enhanced permissions
- **Admin**: Administrative privileges
- **Super Admin**: Full system access

### 4. Location Management
#### Hierarchical Location System:
- **Countries**: Top-level geographic entities
- **Provinces**: State/province level management
- **Districts**: District-level administration
- **LLGs (Local Level Governments)**: Local government areas
- **Wards**: Smallest administrative units

#### Location Features:
- **Complete CRUD**: All location levels manageable
- **Hierarchical Navigation**: Drill-down from country to ward
- **Code Management**: Unique codes for each location
- **Bulk Operations**: Efficient management of multiple locations
- **Geographic Statistics**: Location-based data analytics

### 5. Agricultural Data Management
#### Data Categories:
- **Crops**: Agricultural crop varieties and information
- **Fertilizers**: Fertilizer types and specifications
- **Pesticides**: Pest control products and usage
- **Livestock**: Animal types and characteristics
- **Infections**: Disease and infection data
- **Education**: Educational content and materials

#### Data Management Features:
- **Centralized Control**: Single interface for all agricultural data
- **CRUD Operations**: Full data lifecycle management
- **Data Validation**: Ensure data quality and consistency
- **Bulk Management**: Efficient handling of large datasets
- **Search & Filter**: Easy data discovery and management

### 6. System Administration
#### Administrative Functions:
- **Organization Administrators**: Manage admin users for organizations
- **System Monitoring**: Track system usage and performance
- **Data Integrity**: Ensure data consistency across the system
- **Access Control**: Manage user permissions and access levels

## Technical Implementation

### Controllers
- **DakoiiAuth**: Authentication and session management
- **DakoiiDashboard**: Main dashboard and analytics
- **DakoiiOrganizations**: Organization management
- **DakoiiUsers**: Portal user management
- **DakoiiLocations**: Geographic location management
- **DakoiiData**: Agricultural data management
- **DakoiiSystemAdmins**: System administration functions

### Models
- **DakoiiUsersModel**: Portal user data management
- **DakoiiOrgModel**: Organization data management
- **Location Models**: Country, Province, District, LLG, Ward models
- **Agricultural Data Models**: Crops, Fertilizers, Pesticides, etc.

### Views Structure
- **Template**: `templates/dakoii_template.php`
- **Naming Convention**: `dakoii_[module]_[action].php`
- **Responsive Design**: Mobile and desktop compatible
- **Consistent UI**: Unified interface across all modules

## Security Features

### Authentication Security
- **Password Hashing**: Secure password storage
- **Session Management**: Secure session handling
- **Access Control**: Role-based permissions
- **Session Isolation**: Prevent cross-contamination with main app

### Data Security
- **Input Validation**: Comprehensive form validation
- **SQL Injection Prevention**: Parameterized queries
- **XSS Protection**: Output escaping
- **CSRF Protection**: Form token validation

## User Interface Features

### Navigation
- **Sidebar Menu**: Organized by functional areas
- **Breadcrumb Navigation**: Clear location awareness
- **Quick Actions**: Fast access to common tasks
- **Search Functionality**: Easy data discovery

### Design Elements
- **Responsive Layout**: Works on all device sizes
- **Bootstrap Framework**: Modern, consistent styling
- **Font Awesome Icons**: Clear visual indicators
- **Color-coded Status**: Easy status identification

## Integration Points

### Main Application Integration
- **Shared Database**: Common data tables
- **Location Data**: Synchronized geographic information
- **Agricultural Data**: Centralized data management
- **User Separation**: Independent user systems

### API Endpoints
- **Location APIs**: Dynamic location loading
- **Data APIs**: Agricultural data access
- **Administrative APIs**: System management functions

## Workflow Examples

### Organization Onboarding
1. Create organization profile
2. Set location restrictions
3. Configure license status
4. Assign administrators
5. Activate organization

### Data Management Workflow
1. Access data management dashboard
2. Select data category (crops, fertilizers, etc.)
3. Perform CRUD operations
4. Validate data integrity
5. Monitor usage statistics

### Location Setup Workflow
1. Create/verify country
2. Add provinces
3. Create districts
4. Set up LLGs
5. Define wards
6. Assign to organizations

## Future Enhancement Opportunities
- **Bulk Import/Export**: CSV/Excel data management
- **Advanced Analytics**: Detailed reporting and insights
- **API Documentation**: External integration support
- **Audit Logging**: Comprehensive activity tracking
- **Multi-language Support**: Internationalization
- **Advanced Search**: Full-text search capabilities

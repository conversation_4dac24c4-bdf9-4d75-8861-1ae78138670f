<?= $this->extend('templates/dakoii_template') ?>

<?= $this->section('content') ?>

<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">
                <i class="fas fa-map-marker-alt"></i> Provinces Management
            </h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="<?= base_url('dakoii/dashboard') ?>">Dashboard</a></li>
                    <li class="breadcrumb-item"><a href="<?= base_url('dakoii/locations') ?>">Locations</a></li>
                    <li class="breadcrumb-item active">Provinces</li>
                </ol>
            </nav>
        </div>
        <div class="btn-group">
            <a href="<?= base_url('dakoii/locations/provinces/create') ?>" class="btn btn-primary">
                <i class="fas fa-plus"></i> Add New Province
            </a>
            <a href="<?= base_url('dakoii/locations/provinces/import') ?>" class="btn btn-success">
                <i class="fas fa-upload"></i> Import Provinces CSV
            </a>
            <a href="<?= base_url('dakoii/locations') ?>" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to Locations
            </a>
        </div>
    </div>

    <!-- Flash Messages -->
    <?php if (session()->getFlashdata('success')): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle"></i> <?= session()->getFlashdata('success') ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if (session()->getFlashdata('error')): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle"></i> <?= session()->getFlashdata('error') ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- Provinces Table -->
    <div class="card">
        <div class="card-header">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-list"></i> Provinces List
                </h5>
                <div class="d-flex gap-2">
                    <input type="text" class="form-control form-control-sm" id="searchInput" 
                           placeholder="Search provinces..." style="width: 250px;">
                </div>
            </div>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover mb-0" id="provincesTable">
                    <thead class="table-light">
                        <tr>
                            <th>#</th>
                            <th>Province Name</th>
                            <th>Province Code</th>
                            <th>Country</th>
                            <th>Districts</th>
                            <th>LLGs</th>
                            <th>Wards</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (!empty($provinces)): ?>
                            <?php $i = 1; foreach ($provinces as $province): ?>
                            <tr>
                                <td><?= $i++ ?></td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-map-marker-alt text-primary me-2"></i>
                                        <strong><?= esc($province['name']) ?></strong>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-secondary"><?= esc($province['provincecode']) ?></span>
                                </td>
                                <td>
                                    <span class="text-muted">
                                        <?php
                                        $country = null;
                                        foreach ($countries as $c) {
                                            if ($c['id'] == $province['country_id']) {
                                                $country = $c;
                                                break;
                                            }
                                        }
                                        ?>
                                        <i class="fas fa-globe"></i>
                                        <?= $country ? esc($country['name']) : 'Unknown' ?>
                                    </span>
                                </td>
                                <td>
                                    <a href="<?= base_url('dakoii/locations/districts/' . $province['id']) ?>"
                                       class="text-decoration-none">
                                        <span class="badge bg-info">
                                            <i class="fas fa-building"></i>
                                            <?= $province['districts_count'] ?? 0 ?>
                                        </span>
                                    </a>
                                </td>
                                <td>
                                    <span class="badge bg-warning text-dark">
                                        <i class="fas fa-sitemap"></i>
                                        <?= $province['llgs_count'] ?? 0 ?>
                                    </span>
                                </td>
                                <td>
                                    <span class="badge bg-success">
                                        <i class="fas fa-map"></i>
                                        <?= $province['wards_count'] ?? 0 ?>
                                    </span>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="<?= base_url('dakoii/locations/districts/' . $province['id']) ?>" 
                                           class="btn btn-sm btn-outline-info" title="View Districts">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="<?= base_url('dakoii/locations/provinces/' . $province['id'] . '/edit') ?>" 
                                           class="btn btn-sm btn-outline-primary" title="Edit">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <button type="button" class="btn btn-sm btn-outline-danger" 
                                                onclick="confirmDelete(<?= $province['id'] ?>, '<?= esc($province['name']) ?>')" 
                                                title="Delete">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <tr>
                                <td colspan="8" class="text-center py-4">
                                    <div class="text-muted">
                                        <i class="fas fa-map-marker-alt fa-3x mb-3"></i>
                                        <p>No provinces found</p>
                                        <a href="<?= base_url('dakoii/locations/provinces/create') ?>" class="btn btn-primary">
                                            <i class="fas fa-plus"></i> Add First Province
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
        <?php if (!empty($provinces)): ?>
        <div class="card-footer">
            <div class="d-flex justify-content-between align-items-center">
                <small class="text-muted">
                    Showing <span id="showingCount"><?= count($provinces) ?></span> of <?= count($provinces) ?> provinces
                </small>
            </div>
        </div>
        <?php endif; ?>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete the province <strong id="provinceName"></strong>?</p>
                <p class="text-danger"><small>This action cannot be undone and will affect all related districts, LLGs, and wards.</small></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <button type="submit" class="btn btn-danger">Delete</button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('searchInput');
    const tbody = document.querySelector('#provincesTable tbody');
    const showingCount = document.getElementById('showingCount');

    // Search functionality
    if (searchInput) {
        searchInput.addEventListener('input', filterTable);
    }

    function filterTable() {
        const searchTerm = searchInput.value.toLowerCase();
        const rows = tbody.querySelectorAll('tr');
        let visibleCount = 0;

        rows.forEach(row => {
            const text = row.textContent.toLowerCase();
            if (text.includes(searchTerm)) {
                row.style.display = '';
                visibleCount++;
            } else {
                row.style.display = 'none';
            }
        });

        if (showingCount) {
            showingCount.textContent = visibleCount;
        }
    }
});

function confirmDelete(id, name) {
    document.getElementById('provinceName').textContent = name;
    document.getElementById('deleteForm').action = '<?= base_url('dakoii/locations/provinces/') ?>' + id + '/delete';
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}
</script>

<?= $this->endSection() ?>

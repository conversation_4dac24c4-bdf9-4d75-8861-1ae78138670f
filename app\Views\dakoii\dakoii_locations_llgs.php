<?= $this->extend('templates/dakoii_template') ?>

<?= $this->section('content') ?>

<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">
                <i class="fas fa-landmark"></i> LLGs - <?= esc($district['name']) ?>
            </h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="<?= base_url('dakoii/dashboard') ?>">Dashboard</a></li>
                    <li class="breadcrumb-item"><a href="<?= base_url('dakoii/locations') ?>">Locations</a></li>
                    <li class="breadcrumb-item"><a href="<?= base_url('dakoii/locations/provinces') ?>">Provinces</a></li>
                    <li class="breadcrumb-item"><a href="<?= base_url('dakoii/locations/districts/' . $province['id']) ?>"><?= esc($province['name']) ?> Districts</a></li>
                    <li class="breadcrumb-item active"><?= esc($district['name']) ?> LLGs</li>
                </ol>
            </nav>
        </div>
        <div class="btn-group">
            <a href="<?= base_url('dakoii/locations/llgs/create/' . $district['id']) ?>" class="btn btn-primary">
                <i class="fas fa-plus"></i> Add New LLG
            </a>
            <a href="<?= base_url('dakoii/locations/llgs/import/' . $district['id']) ?>" class="btn btn-success">
                <i class="fas fa-upload"></i> Import LLGs CSV
            </a>
            <a href="<?= base_url('dakoii/locations/districts/' . $province['id']) ?>" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to Districts
            </a>
        </div>
    </div>

    <!-- District Info Card -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-info">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-8">
                            <h5 class="card-title text-info">
                                <i class="fas fa-building"></i> <?= esc($district['name']) ?>
                            </h5>
                            <p class="card-text">
                                <span class="badge bg-secondary me-2">Code: <?= esc($district['districtcode']) ?></span>
                                <span class="badge bg-info me-2">JSON ID: <?= esc($district['json_id']) ?></span>
                                <span class="badge bg-primary me-2">Province: <?= esc($province['name']) ?></span>
                            </p>
                        </div>
                        <div class="col-md-4 text-end">
                            <div class="row">
                                <div class="col-6">
                                    <div class="text-center">
                                        <h4 class="text-warning"><?= count($llgs) ?></h4>
                                        <small class="text-muted">LLGs</small>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="text-center">
                                        <h4 class="text-success"><?= array_sum(array_column($llgs, 'wards_count')) ?></h4>
                                        <small class="text-muted">Wards</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Flash Messages -->
    <?php if (session()->getFlashdata('success')): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle"></i> <?= session()->getFlashdata('success') ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if (session()->getFlashdata('error')): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle"></i> <?= session()->getFlashdata('error') ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- LLGs Table -->
    <div class="card">
        <div class="card-header">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-list"></i> LLGs List
                </h5>
                <div class="d-flex gap-2">
                    <input type="text" class="form-control form-control-sm" id="searchInput" 
                           placeholder="Search LLGs..." style="width: 250px;">
                </div>
            </div>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover mb-0" id="llgsTable">
                    <thead class="table-light">
                        <tr>
                            <th>#</th>
                            <th>LLG Name</th>
                            <th>LLG Code</th>
                            <th>JSON ID</th>
                            <th>Wards</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (!empty($llgs)): ?>
                            <?php $i = 1; foreach ($llgs as $llg): ?>
                            <tr>
                                <td><?= $i++ ?></td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-landmark text-warning me-2"></i>
                                        <strong><?= esc($llg['name']) ?></strong>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-secondary"><?= esc($llg['llgcode']) ?></span>
                                </td>
                                <td>
                                    <span class="text-muted"><?= esc($llg['json_id']) ?></span>
                                </td>
                                <td>
                                    <a href="<?= base_url('dakoii/locations/wards/' . $llg['id']) ?>" 
                                       class="text-decoration-none">
                                        <span class="badge bg-success">
                                            <i class="fas fa-map"></i> 
                                            <?= $llg['wards_count'] ?? 0 ?>
                                        </span>
                                    </a>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="<?= base_url('dakoii/locations/wards/' . $llg['id']) ?>" 
                                           class="btn btn-sm btn-outline-info" title="View Wards">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="<?= base_url('dakoii/locations/llgs/edit/' . $llg['id']) ?>" 
                                           class="btn btn-sm btn-outline-primary" title="Edit">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <button type="button" class="btn btn-sm btn-outline-danger" 
                                                onclick="confirmDelete(<?= $llg['id'] ?>, '<?= esc($llg['name']) ?>')" 
                                                title="Delete">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <tr>
                                <td colspan="6" class="text-center py-4">
                                    <div class="text-muted">
                                        <i class="fas fa-landmark fa-3x mb-3"></i>
                                        <p>No LLGs found in <?= esc($district['name']) ?></p>
                                        <a href="<?= base_url('dakoii/locations/llgs/create/' . $district['id']) ?>" class="btn btn-primary">
                                            <i class="fas fa-plus"></i> Add First LLG
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
        <?php if (!empty($llgs)): ?>
        <div class="card-footer">
            <div class="d-flex justify-content-between align-items-center">
                <small class="text-muted">
                    Showing <span id="showingCount"><?= count($llgs) ?></span> of <?= count($llgs) ?> LLGs
                </small>
            </div>
        </div>
        <?php endif; ?>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete the LLG <strong id="llgName"></strong>?</p>
                <p class="text-danger"><small>This action cannot be undone and will affect all related wards.</small></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <button type="submit" class="btn btn-danger">Delete</button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('searchInput');
    const tbody = document.querySelector('#llgsTable tbody');
    const showingCount = document.getElementById('showingCount');

    // Search functionality
    if (searchInput) {
        searchInput.addEventListener('input', filterTable);
    }

    function filterTable() {
        const searchTerm = searchInput.value.toLowerCase();
        const rows = tbody.querySelectorAll('tr');
        let visibleCount = 0;

        rows.forEach(row => {
            const text = row.textContent.toLowerCase();
            if (text.includes(searchTerm)) {
                row.style.display = '';
                visibleCount++;
            } else {
                row.style.display = 'none';
            }
        });

        if (showingCount) {
            showingCount.textContent = visibleCount;
        }
    }
});

function confirmDelete(id, name) {
    document.getElementById('llgName').textContent = name;
    document.getElementById('deleteForm').action = '<?= base_url('dakoii/locations/llgs/') ?>' + id + '/delete';
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}
</script>

<?= $this->endSection() ?>

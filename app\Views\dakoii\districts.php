<?= $this->extend("templates/dakoiiadmin"); ?>
<?= $this->section('content'); ?>

<div class="container-fluid p-2">
    <div class="row">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-info text-white d-flex justify-content-between align-items-center">
                    <div>
                        <h5 class="mb-0">
                            <i class="fas fa-map"></i> Districts of <?= esc($province['name']) ?>
                        </h5>
                        <small class="text-white-50">Province Code: <?= esc($province['provincecode']) ?></small>
                    </div>
                    <div>
                        <a href="<?= base_url('location-management/provinces') ?>" class="btn btn-light mr-2">
                            <i class="fas fa-arrow-left"></i> Back to Provinces
                        </a>
                        <a href="<?= base_url('location-management/districts/create/' . $province['id']) ?>" class="btn btn-success">
                            <i class="fas fa-plus"></i> Add District
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <table class="table table-hover">
                        <thead class="thead-dark">
                            <tr>
                                <th>#</th>
                                <th>District Code</th>
                                <th>JSON ID</th>
                                <th>Name</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $i = 1;
                            foreach ($districts as $district): ?>
                                <tr>
                                    <td><?= $i++ ?></td>

                                    <td><?= esc($district['districtcode']) ?></td>
                                    <td><?= esc($district['json_id']) ?></td>
                                    <td><?= esc($district['name']) ?></td>
                                    <td>
                                        <a href="<?= base_url('llgs/' . $district['id']) ?>"
                                            class="btn btn-sm btn-info"
                                            title="Manage LLGs">
                                            <i class="fas fa-landmark"></i>
                                        </a>
                                        <a href="<?= base_url('location-management/districts/edit/' . $district['id']) ?>"
                                           class="btn btn-sm btn-primary">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="<?= base_url('location-management/districts/delete/' . $district['id']) ?>"
                                            class="btn btn-sm btn-danger"
                                            onclick="return confirm('Are you sure you want to delete the district <?= esc($district['name']) ?> from <?= esc($province['name']) ?> province? This action cannot be undone.')">
                                            <i class="fas fa-trash"></i>
                                        </a>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-map-marker-alt"></i> District JSON Data</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table id="districtJsonTable" class="table table-bordered table-hover">
                            <thead class="thead-light">
                                <tr>
                                    <th>FID</th>
                                    <th>GEOCODE</th>
                                    <th>District Name</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php
                                $json_file = file_get_contents(base_url(JSON_MAP_DISTRICT));
                                $data = json_decode($json_file, true);

                                foreach ($data['features'] as $feature) {
                                    $properties = $feature['properties'];
                                ?>
                                    <tr>
                                        <td><?= esc($properties['FID']) ?></td>
                                        <td><?= esc($properties['GEOCODE']) ?></td>
                                        <td><?= esc($properties['DISTNAME']) ?></td>
                                    </tr>
                                <?php
                                }
                                ?>
                            </tbody>
                        </table>
                    </div>

                    <script>
                        $(document).ready(function() {
                            $('#districtJsonTable').DataTable({
                                "pageLength": 10,
                                "ordering": true,
                                "searching": true,
                                "responsive": true
                            });
                        });
                    </script>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add District Modal -->
<div class="modal fade" id="addDistrictModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-success text-white">
                <h5 class="modal-title">Add New District</h5>
                <button type="button" class="close text-white" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <?= form_open('location-management/districts/store') ?>
            <div class="modal-body">
                <input type="hidden" name="country_id" value="<?= $province['country_id'] ?>">
                <input type="hidden" name="province_id" value="<?= $province['id'] ?>">

                <div class="form-group">
                    <label>District Code</label>
                    <input type="text" class="form-control" name="districtcode" required>
                </div>

                <div class="form-group">
                    <label>District Name</label>
                    <input type="text" class="form-control" name="name" required>
                </div>
                <div class="form-group">
                    <label>JSON ID</label>
                    <input type="text" class="form-control" name="json_id" required>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                <button type="submit" class="btn btn-success">Save District</button>
            </div>
            <?= form_close() ?>
        </div>
    </div>
</div>



<?= $this->endSection() ?>